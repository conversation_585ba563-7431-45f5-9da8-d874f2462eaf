import { Component, HostListener, OnInit, ElementRef } from '@angular/core';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.css']
})
export class AboutComponent implements OnInit {
  maxScroll = 500;

  constructor(private el: ElementRef) {}

  ngOnInit(): void {}

  @HostListener('window:scroll', [])
  onWindowScroll(): void {
    const scrollY = window.scrollY;
    const iframe = this.el.nativeElement.querySelector('.spline-left-wrapper iframe');

    if (iframe) {
      if (scrollY >= this.maxScroll) {
        iframe.style.pointerEvents = 'none'; // to freeze interaction
        iframe.style.filter = 'grayscale(100%)'; // optional visual effect
      } else {
        iframe.style.pointerEvents = 'auto';
        iframe.style.filter = 'grayscale(0%)';
      }
    }
  }
}
